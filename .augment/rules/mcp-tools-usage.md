---
type: "always_apply"
---

# MCP Tools Usage Guidelines - Ông Ba Dạy Hóa Project

## 🎯 MCP Tools Integration Strategy
You have access to powerful MCP (Model Context Protocol) tools that should be leveraged strategically for the Ông Ba Dạy Hóa project development.

## 🔧 Available MCP Tools & When to Use

### 1. **Context7 Library Research**
**Tools:** `resolve-library-id_Context_7`, `get-library-docs_Context_7`

**Use for:**
- Researching Next.js 15 App Router patterns and best practices
- Learning Strapi 5 advanced features and TypeScript integration
- Finding MySQL optimization techniques for educational platforms
- Discovering security best practices for authentication and payments
- Exploring integration patterns between technologies

**Example Usage:**
```
When implementing new features:
1. Use resolve-library-id to find relevant documentation
2. Use get-library-docs to get specific implementation details
3. Apply learnings to project-specific context
```

### 2. **Web Research & Documentation**
**Tools:** `firecrawl_scrape`, `firecrawl_search`, `brave_web_search`, `brave_local_search`

**Use for:**
- Researching Dokploy deployment best practices
- Finding Cloudflare R2 integration patterns
- Learning about PayOS payment gateway implementation
- Discovering Vietnamese market-specific requirements
- Staying updated with latest tech stack developments

**Priority Order:**
1. `brave_web_search` - For general technical research
2. `firecrawl_search` - For deep technical documentation
3. `firecrawl_scrape` - For specific documentation pages
4. `brave_local_search` - For Vietnamese market research

### 3. **Codebase Analysis & Development**
**Tools:** `codebase-retrieval`, `git-commit-retrieval`, `str-replace-editor`, `save-file`

**Use for:**
- Understanding existing patterns before implementing new features
- Finding similar implementations in the codebase
- Learning from previous changes and commits
- Implementing new features following project conventions

**Workflow:**
1. Always use `codebase-retrieval` before making changes
2. Use `git-commit-retrieval` to understand how similar features were implemented
3. Use `str-replace-editor` for modifications, `save-file` for new files

### 4. **Browser Automation & Testing (Playwright)**
**Tools:** `browser_navigate`, `browser_snapshot`, `browser_click`, `browser_type`, `browser_wait_for`, `browser_take_screenshot`, `browser_evaluate`, `browser_hover`, `browser_select_option`, `browser_drag`, `browser_press_key`, `browser_handle_dialog`, `browser_tab_*`, `browser_resize`, `browser_console_messages`, `browser_network_requests`

**Core Use Cases:**
- **E2E Testing:** Complete user workflows (đăng ký → đăng nhập → mua khóa học → học bài)
- **UI/UX Validation:** Component rendering, responsive design, accessibility
- **Performance Testing:** Page load times, resource loading, memory usage
- **Security Testing:** Authentication flows, authorization checks, XSS prevention
- **Integration Testing:** API interactions, payment flows, email workflows
- **Regression Testing:** Verify fixes don't break existing functionality

**Playwright Workflow Pattern:**
```javascript
// 1. Navigate to page
browser_navigate(url)

// 2. Take snapshot for element identification
browser_snapshot()

// 3. Interact with elements
browser_click(element, ref)
browser_type(element, ref, text)

// 4. Wait for responses/changes
browser_wait_for(text/time)

// 5. Verify results
browser_snapshot() // or browser_take_screenshot()
```

**Vietnamese Context Testing:**
- **URL Testing:** `/dang-nhap`, `/khoa-hoc`, `/quan-ly` routing
- **Language Support:** UTF-8 Vietnamese characters in forms
- **Cultural UX:** Vietnamese educational platform expectations
- **Mobile-First:** Test on Vietnamese mobile usage patterns

### 5. **Playwright Testing Scenarios for Ông Ba Dạy Hóa**

**Authentication Flow Testing:**
```javascript
// Complete login flow
browser_navigate("https://ongbadayhoa.com/dang-nhap")
browser_snapshot() // Identify form elements
browser_type("email input", "ref_id", "<EMAIL>")
browser_type("password input", "ref_id", "password123")
browser_click("login button", "ref_id")
browser_wait_for("Dashboard loaded") // Wait for redirect
browser_snapshot() // Verify successful login
```

**Course Enrollment Testing:**
```javascript
// Test course purchase flow
browser_navigate("https://ongbadayhoa.com/khoa-hoc/hoa-hoc-12")
browser_snapshot()
browser_click("Mua khóa học button", "ref_id")
browser_wait_for("Payment form")
browser_type("payment details", "ref_id", "test_data")
browser_click("Thanh toán button", "ref_id")
browser_wait_for("Success message")
browser_snapshot() // Verify enrollment success
```

**Learning Progress Testing:**
```javascript
// Test lesson completion
browser_navigate("https://ongbadayhoa.com/hoc-bai/lesson-1")
browser_snapshot()
browser_click("Play video button", "ref_id")
browser_wait_for(30) // Wait for video progress
browser_click("Mark complete button", "ref_id")
browser_wait_for("Progress updated")
browser_snapshot() // Verify progress tracking
```

**Admin Panel Testing:**
```javascript
// Test admin functions
browser_navigate("https://ongbadayhoa.com/quan-ly")
browser_snapshot()
browser_click("Quản lý khóa học", "ref_id")
browser_click("Thêm khóa học mới", "ref_id")
browser_type("course title", "ref_id", "Khóa học mới")
browser_type("course description", "ref_id", "Mô tả khóa học")
browser_click("Lưu khóa học", "ref_id")
browser_wait_for("Course created successfully")
browser_snapshot()
```

### 6. **Playwright Best Practices & Patterns**

**Element Selection Strategy:**
- **Always use browser_snapshot() first** to identify elements and get ref IDs
- **Use descriptive element descriptions** in Vietnamese for clarity
- **Wait for elements to be ready** before interaction
- **Handle dynamic content** with proper wait conditions

**Interaction Patterns:**
```javascript
// Good pattern: Wait → Snapshot → Interact → Verify
browser_wait_for("Page loaded")
browser_snapshot()
browser_click("Descriptive element name", "exact_ref_id")
browser_wait_for("Expected result")
browser_snapshot() // Verify the action worked
```

**Error Handling:**
```javascript
// Handle dialogs and popups
browser_handle_dialog(true) // Accept confirmation dialogs
browser_wait_for("Dialog dismissed")

// Handle loading states
browser_wait_for("Loading spinner gone")
browser_snapshot() // Verify content loaded
```

**Responsive Testing:**
```javascript
// Test mobile responsiveness
browser_resize(375, 667) // iPhone SE size
browser_snapshot()
browser_resize(1920, 1080) // Desktop size
browser_snapshot()
```

### 7. **Playwright Error Handling & Debugging**

**Common Issues & Solutions:**
- **Element not found:** Always use browser_snapshot() to get current page state
- **Timing issues:** Use browser_wait_for() with specific conditions
- **Dialog handling:** Use browser_handle_dialog() for confirmations
- **Network issues:** Check browser_network_requests() for failed requests

**Debugging Workflow:**
```javascript
// Debug failing tests
browser_console_messages() // Check for JavaScript errors
browser_network_requests() // Check for failed API calls
browser_take_screenshot() // Visual debugging
browser_evaluate("() => window.location.href") // Check current URL
```

**Performance Monitoring:**
```javascript
// Monitor page performance
browser_evaluate(`() => {
  return {
    loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
    domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
  }
}`)
```

### 8. **Playwright Security & Performance Testing**

**Security Testing Patterns:**
```javascript
// Test XSS prevention
browser_type("search input", "ref_id", "<script>alert('xss')</script>")
browser_click("search button", "ref_id")
browser_wait_for(2)
browser_console_messages() // Check for XSS execution

// Test authentication bypass
browser_navigate("https://ongbadayhoa.com/quan-ly") // Protected route
browser_snapshot() // Should redirect to login
```

**Performance Testing:**
```javascript
// Test page load performance
const startTime = Date.now()
browser_navigate("https://ongbadayhoa.com/khoa-hoc")
browser_wait_for("Course list loaded")
const loadTime = Date.now() - startTime
// Verify loadTime < 3000ms for good UX
```

**Load Testing Simulation:**
```javascript
// Test with multiple tabs (simulate concurrent users)
browser_tab_new("https://ongbadayhoa.com/dang-nhap")
browser_tab_new("https://ongbadayhoa.com/khoa-hoc")
browser_tab_new("https://ongbadayhoa.com/quan-ly")
// Test each tab functionality
browser_tab_select(0)
browser_snapshot()
```

## 🎯 Project-Specific MCP Usage Patterns

### For Next.js Development:
1. Research with Context7: "Next.js 15 App Router"
2. Find specific patterns: Server Components, Client Components
3. Apply to Vietnamese URLs: /dang-nhap, /khoa-hoc, /quan-ly
4. Test with browser tools for responsive design

### For Strapi Development:
1. Research with Context7: "Strapi 5 TypeScript"
2. Find content type patterns and relationships
3. Implement custom controllers/services
4. Test API endpoints with browser tools

### For Database Optimization:
1. Research with Context7: "MySQL performance optimization"
2. Find indexing strategies for educational platforms
3. Apply to current schema with 1K+ users in mind
4. Monitor performance with appropriate tools

### For Infrastructure & Deployment:
1. Research Dokploy best practices with web search
2. Find Cloudflare R2 integration patterns
3. Implement following current deployment workflow
4. Test deployment process

## 🚀 MCP Tools Workflow for Common Tasks

### Task 1: Implementing New Feature
1. codebase-retrieval: Analyze existing similar features
2. git-commit-retrieval: Learn from previous implementations
3. Context7: Research best practices for the technology
4. Web search: Find additional resources if needed
5. str-replace-editor: Implement following project patterns
6. Browser tools: Test the implementation

### Task 2: Debugging Issues
1. codebase-retrieval: Understand the problematic code
2. git-commit-retrieval: Check recent changes that might cause issues
3. Browser tools: Reproduce and analyze the issue
4. Web search: Find solutions for similar problems
5. str-replace-editor: Apply fixes
6. Browser tools: Verify the fix works

### Task 3: Performance Optimization
1. Context7: Research optimization techniques
2. codebase-retrieval: Analyze current implementation
3. Web search: Find specific optimization strategies
4. str-replace-editor: Implement optimizations
5. Browser tools: Test performance improvements

### Task 4: Security Hardening
1. Context7: Research security best practices
2. Web search: Find latest security vulnerabilities and fixes
3. codebase-retrieval: Audit current security implementations
4. str-replace-editor: Apply security improvements
5. Browser tools: Test security measures

## 🛡️ MCP Tools Best Practices

### Research Strategy:
- **Start broad, then narrow:** Use web search first, then Context7 for specifics
- **Verify information:** Cross-reference multiple sources
- **Apply to context:** Always consider project-specific requirements
- **Document findings:** Save important discoveries for future reference

### Development Strategy:
- **Understand first:** Always analyze existing code before changes
- **Follow patterns:** Use codebase-retrieval to maintain consistency
- **Test thoroughly:** Use browser tools to verify implementations
- **Consider scale:** Remember the 1K user base and growth trajectory

### Security Strategy:
- **Research first:** Use Context7 and web search for security best practices
- **Audit regularly:** Use codebase-retrieval to check for vulnerabilities
- **Test security:** Use browser tools to verify security measures
- **Stay updated:** Regular web search for new security threats

## 📊 MCP Tools Success Metrics

### Research Effectiveness:
- Find relevant, up-to-date information quickly
- Apply research findings to project successfully
- Reduce implementation time through better preparation

### Development Efficiency:
- Maintain code consistency across the project
- Reduce bugs through better understanding of existing code
- Implement features that integrate well with existing architecture

### Quality Assurance:
- Catch issues early through thorough testing
- Ensure responsive design works across devices
- Verify security measures are properly implemented

## 🎪 Communication About MCP Usage

### When Using MCP Tools:
- **Explain the research:** Share what you learned and why it's relevant
- **Show the process:** Explain how you used the tools to reach conclusions
- **Provide context:** Connect findings to project-specific requirements
- **Suggest next steps:** Recommend follow-up actions based on findings

### Example Communication:
"Tôi đã research Next.js 15 App Router patterns với Context7 và tìm thấy best practices cho Server Components. Dựa vào codebase analysis, tôi thấy chúng ta có thể optimize trang /khoa-hoc bằng cách implement Server Components cho course listing. Tôi sẽ test implementation này với browser tools để đảm bảo performance tốt cho 1K users hiện tại."

---

*Use MCP tools strategically to enhance development efficiency while maintaining the high quality standards of the Ông Ba Dạy Hóa project.*
